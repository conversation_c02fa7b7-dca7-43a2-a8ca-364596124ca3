import { test as base, expect, Page } from '@playwright/test';

// Define custom fixtures interface
interface CustomFixtures {
  authenticatedPage: Page;
  testDataCleanup: Page;
}

// Extend the base test with custom fixtures
export const test = base.extend<CustomFixtures>({
  // Custom fixture for authenticated page
  authenticatedPage: async ({ page }, use) => {
    // Login before each test that uses this fixture
    await page.goto('/auth/login');
    await page.fill('[data-testid="username"]', 'admin');
    await page.fill('[data-testid="password"]', 'password');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/admin/**');

    await use(page);
  },

  // Custom fixture for test data cleanup
  testDataCleanup: async ({ page }, use) => {
    await use(page);

    // Cleanup test data after each test
    await cleanupTestData(page);
  }
});

// Helper function to clean up test data
async function cleanupTestData(page: any) {
  try {
    // Navigate to employees page and delete test employees
    await page.goto('/admin/hr/employees');
    await page.waitForSelector('generic-p-table', { timeout: 5000 });
    
    // Find and delete test employees (those with codes starting with TEST)
    const testEmployeeRows = page.locator('tr:has-text("TEST")');
    const count = await testEmployeeRows.count();
    
    for (let i = 0; i < count; i++) {
      const deleteButton = testEmployeeRows.nth(i).locator('button[icon="pi pi-trash"]');
      if (await deleteButton.count() > 0) {
        await deleteButton.click();
        await page.waitForSelector('p-confirmdialog');
        await page.click('[data-testid="confirm-delete-button"]');
        await page.waitForSelector('.p-toast-message-success', { timeout: 5000 });
        await page.waitForTimeout(500); // Brief pause between deletions
      }
    }

    // Clean up test departments
    await page.goto('/admin/system/settings/departments');
    await page.waitForSelector('p-treetable', { timeout: 5000 });
    
    const testDepartmentRows = page.locator('tr:has-text("Test Department")');
    const deptCount = await testDepartmentRows.count();
    
    for (let i = 0; i < deptCount; i++) {
      const deleteButton = testDepartmentRows.nth(i).locator('button[icon="pi pi-trash"]');
      if (await deleteButton.count() > 0) {
        await deleteButton.click();
        await page.waitForSelector('p-confirmdialog');
        await page.click('[data-testid="confirm-delete-button"]');
        await page.waitForSelector('.p-toast-message-success', { timeout: 5000 });
        await page.waitForTimeout(500);
      }
    }

    // Clean up test positions
    await page.goto('/admin/system/settings/positions');
    await page.waitForSelector('p-table', { timeout: 5000 });
    
    const testPositionRows = page.locator('tr:has-text("Test Position")');
    const posCount = await testPositionRows.count();
    
    for (let i = 0; i < posCount; i++) {
      const deleteButton = testPositionRows.nth(i).locator('button[icon="pi pi-trash"]');
      if (await deleteButton.count() > 0) {
        await deleteButton.click();
        await page.waitForSelector('p-confirmdialog');
        await page.click('[data-testid="confirm-delete-button"]');
        await page.waitForSelector('.p-toast-message-success', { timeout: 5000 });
        await page.waitForTimeout(500);
      }
    }
  } catch (error) {
    console.warn('Cleanup failed:', error);
    // Don't fail the test if cleanup fails
  }
}

// Custom matchers for better assertions
expect.extend({
  async toHaveValidationError(locator: any, expected?: string) {
    const hasInvalidClass = await locator.evaluate((el: Element) => 
      el.classList.contains('p-invalid')
    );
    
    if (expected) {
      const errorMessage = await locator.locator('+ .p-error').textContent();
      return {
        message: () => `Expected validation error "${expected}" but got "${errorMessage}"`,
        pass: hasInvalidClass && errorMessage?.includes(expected)
      };
    }
    
    return {
      message: () => `Expected element to have validation error`,
      pass: hasInvalidClass
    };
  },

  async toHaveToastMessage(page: any, type: 'success' | 'error' | 'warn' | 'info', message?: string) {
    const toastSelector = `.p-toast-message-${type}`;
    const toast = page.locator(toastSelector);
    
    const isVisible = await toast.isVisible();
    
    if (message) {
      const toastText = await toast.textContent();
      return {
        message: () => `Expected ${type} toast with message "${message}" but got "${toastText}"`,
        pass: isVisible && toastText?.includes(message)
      };
    }
    
    return {
      message: () => `Expected ${type} toast to be visible`,
      pass: isVisible
    };
  }
});

// Global test configuration
export const TEST_CONFIG = {
  // Default timeouts
  defaultTimeout: 30000,
  navigationTimeout: 10000,
  
  // Test data
  testUsers: {
    admin: {
      username: 'admin',
      password: 'password'
    },
    employee: {
      username: 'employee',
      password: 'password'
    }
  },
  
  // API endpoints
  apiEndpoints: {
    employees: '/api/employees',
    departments: '/api/departments',
    positions: '/api/positions',
    employeePositions: '/api/employee-positions'
  },
  
  // Selectors
  selectors: {
    // Dialog selectors
    editCreateDialog: 'edit-create-dialog[visible="true"]',
    confirmButton: '[data-testid="confirm-button"]',
    cancelButton: '[data-testid="cancel-button"]',
    
    // Table selectors
    genericTable: 'generic-p-table',
    tableRows: 'tbody tr',
    
    // Toast selectors
    successToast: '.p-toast-message-success',
    errorToast: '.p-toast-message-error',
    
    // Form selectors
    invalidField: '.p-invalid',
    errorMessage: '.p-error'
  }
};

// Utility functions for common test operations
export const TestUtils = {
  // Wait for API response
  async waitForApiResponse(page: any, endpoint: string, timeout = 5000) {
    return page.waitForResponse(
      (response: any) => response.url().includes(endpoint) && response.status() === 200,
      { timeout }
    );
  },

  // Fill form with data
  async fillForm(page: any, formData: Record<string, string>) {
    for (const [field, value] of Object.entries(formData)) {
      await page.fill(`[data-testid="${field}"]`, value);
    }
  },

  // Wait for table to load
  async waitForTableLoad(page: any, tableSelector = 'generic-p-table') {
    await page.waitForSelector(tableSelector);
    await page.waitForSelector('.p-datatable-loading-overlay', { state: 'hidden' });
  },

  // Create test employee
  async createTestEmployee(page: any, employeeData: any) {
    await page.goto('/admin/hr/employees');
    await this.waitForTableLoad(page);
    
    await page.click('[data-testid="new-employee-button"]');
    await page.waitForSelector(TEST_CONFIG.selectors.editCreateDialog);
    
    await this.fillForm(page, employeeData);
    await page.click(TEST_CONFIG.selectors.confirmButton);
    await page.waitForSelector(TEST_CONFIG.selectors.successToast);
  },

  // Delete test employee
  async deleteTestEmployee(page: any, employeeCode: string) {
    await page.goto('/admin/hr/employees');
    await this.waitForTableLoad(page);
    
    const deleteButton = page.locator(`tr:has-text("${employeeCode}") button[icon="pi pi-trash"]`);
    if (await deleteButton.count() > 0) {
      await deleteButton.click();
      await page.waitForSelector('p-confirmdialog');
      await page.click('[data-testid="confirm-delete-button"]');
      await page.waitForSelector(TEST_CONFIG.selectors.successToast);
    }
  }
};

export { expect };
