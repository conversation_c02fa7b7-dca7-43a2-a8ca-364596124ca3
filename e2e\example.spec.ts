import { test, expect } from './test-setup';

test.describe('Basic Application Tests', () => {
  test('should load the application and show correct title', async ({ page }) => {
    await page.goto('/');

    // Expect a title "to contain" a substring.
    await expect(page).toHaveTitle(/Weset Portal/);
  });

  test('should redirect to login page when not authenticated', async ({ page }) => {
    await page.goto('/admin');

    // Should redirect to login
    await expect(page).toHaveURL(/.*auth\/login.*/);
  });

  test('should show login form elements', async ({ page }) => {
    await page.goto('/auth/login');

    // Check for login form elements
    await expect(page.locator('[data-testid="username"]')).toBeVisible();
    await expect(page.locator('[data-testid="password"]')).toBeVisible();
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
  });

  test('should handle invalid login credentials', async ({ page }) => {
    await page.goto('/auth/login');

    // Fill invalid credentials
    await page.fill('[data-testid="username"]', 'invalid');
    await page.fill('[data-testid="password"]', 'invalid');
    await page.click('[data-testid="login-button"]');

    // Should show error message
    await expect(page.locator('.p-toast-message-error')).toBeVisible();
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    await page.goto('/auth/login');

    // Fill valid credentials
    await page.fill('[data-testid="username"]', 'admin');
    await page.fill('[data-testid="password"]', 'password');
    await page.click('[data-testid="login-button"]');

    // Should redirect to admin dashboard
    await expect(page).toHaveURL(/.*admin.*/);
  });

  test('should show navigation menu after login', async ({ authenticatedPage }) => {
    // Using the authenticated page fixture
    await expect(authenticatedPage.locator('nav')).toBeVisible();
    await expect(authenticatedPage.locator('text=HR')).toBeVisible();
    await expect(authenticatedPage.locator('text=System')).toBeVisible();
  });

  test('should handle logout functionality', async ({ authenticatedPage }) => {
    // Find and click logout button
    const logoutButton = authenticatedPage.locator('[data-testid="logout-button"]');
    if (await logoutButton.count() > 0) {
      await logoutButton.click();

      // Should redirect to login page
      await expect(authenticatedPage).toHaveURL(/.*auth\/login.*/);
    }
  });
});
