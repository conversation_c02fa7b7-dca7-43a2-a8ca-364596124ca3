import { test, expect, Page } from '@playwright/test';

// Test data constants
const TEST_DATA = {
  employee: {
    code: '<PERSON>MP001',
    fullname: '<PERSON>',
    nickname: '<PERSON>',
    email: '<EMAIL>',
    phone: '0123456789',
    identification: '123456789012'
  },
  department: {
    code: 'DEPT001',
    name: 'Test Department'
  },
  position: {
    code: 'POS001',
    name: 'Test Position'
  }
};

// Helper functions
async function login(page: Page) {
  // Navigate to login page
  await page.goto('/auth/login');

  // Fill login form using actual selectors
  await page.fill('#username', 'admin');
  await page.fill('#password1 input', '12345678');
  await page.click('p-button[label="Đăng nhập"]');

  // Wait for navigation to dashboard
  await page.waitForURL('**/**');
}

async function navigateToEmployees(page: Page) {
  await page.goto('/hr/employees');
  await page.waitForSelector('generic-p-table');
}

async function navigateToDepartments(page: Page) {
  await page.goto('/system/settings/departments');
  await page.waitForSelector('p-treetable');
}

async function navigateToPositions(page: Page) {
  await page.goto('/system/settings/positions');
  await page.waitForSelector('p-table');
}

test.describe('Employee CRUD Operations', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await navigateToEmployees(page);
  });

  test('should create a new employee', async ({ page }) => {
    // Click "New" button to open creation dialog - using the actual toolbar button
    await page.click('button:has(span.pi-plus)');

    // Wait for dialog to appear
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Fill employee form using actual form field keys
    await page.fill('input[formcontrolname="code"]', TEST_DATA.employee.code);
    await page.fill('input[formcontrolname="fullname"]', TEST_DATA.employee.fullname);
    await page.fill('input[formcontrolname="nickname"]', TEST_DATA.employee.nickname);
    await page.fill('input[formcontrolname="email"]', TEST_DATA.employee.email);
    await page.fill('input[formcontrolname="phone"]', TEST_DATA.employee.phone);
    await page.fill('input[formcontrolname="identification"]', TEST_DATA.employee.identification);

    // Click save button - using the actual confirm button
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify employee appears in table
    await page.waitForSelector(`text=${TEST_DATA.employee.fullname}`);
    expect(await page.textContent('generic-p-table')).toContain(TEST_DATA.employee.fullname);
  });

  test('should edit an existing employee', async ({ page }) => {
    // Find and click edit button for the test employee
    const editButton = page.locator(`tr:has-text("${TEST_DATA.employee.fullname}") button[icon="pi pi-pencil"]`);
    await editButton.click();

    // Wait for edit dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Modify employee data
    const updatedName = TEST_DATA.employee.fullname + ' Updated';
    await page.fill('input[formcontrolname="fullname"]', updatedName);

    // Save changes
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify updated name appears in table
    await page.waitForSelector(`text=${updatedName}`);
    expect(await page.textContent('generic-p-table')).toContain(updatedName);
  });

  test('should delete an employee', async ({ page }) => {
    // Find and click delete button for the test employee
    const deleteButton = page.locator(`tr:has-text("${TEST_DATA.employee.fullname}") button[icon="pi pi-trash"]`);
    await deleteButton.click();

    // Wait for confirmation dialog
    await page.waitForSelector('p-confirmdialog');

    // Confirm deletion - using the actual confirm dialog button
    await page.click('p-confirmdialog button.p-confirm-dialog-accept');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify employee is removed from table
    await page.waitForTimeout(1000); // Wait for table refresh
    expect(await page.textContent('generic-p-table')).not.toContain(TEST_DATA.employee.fullname);
  });

  test('should validate required fields in employee form', async ({ page }) => {
    // Click "New" button
    await page.click('button:has(span.pi-plus)');

    // Wait for dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Try to save without filling required fields
    await page.click('p-button[label="Xác nhận"]');

    // Verify validation errors appear
    await page.waitForSelector('.p-invalid');
    expect(await page.locator('.p-invalid').count()).toBeGreaterThan(0);

    // Cancel dialog
    await page.click('p-button[label="Hủy"]');
  });
});

test.describe('Department CRUD Operations', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await navigateToDepartments(page);
  });

  test('should create a new department', async ({ page }) => {
    // Click "New" button - using the actual toolbar button
    await page.click('button:has-text("Thêm phòng ban")');

    // Wait for dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Fill department form
    await page.fill('input[formcontrolname="code"]', TEST_DATA.department.code);
    await page.fill('input[formcontrolname="name"]', TEST_DATA.department.name);

    // Save department
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify department appears in tree table
    await page.waitForSelector(`text=${TEST_DATA.department.name}`);
    expect(await page.textContent('p-treetable')).toContain(TEST_DATA.department.name);
  });

  test('should edit a department', async ({ page }) => {
    // Find and click edit button
    const editButton = page.locator(`tr:has-text("${TEST_DATA.department.name}") button[icon="pi pi-pencil"]`);
    await editButton.click();

    // Wait for edit dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Update department name
    const updatedName = TEST_DATA.department.name + ' Updated';
    await page.fill('input[formcontrolname="name"]', updatedName);

    // Save changes
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify updated name
    await page.waitForSelector(`text=${updatedName}`);
    expect(await page.textContent('p-treetable')).toContain(updatedName);
  });

  test('should delete a department', async ({ page }) => {
    // Find and click delete button
    const deleteButton = page.locator(`tr:has-text("${TEST_DATA.department.name}") button[icon="pi pi-trash"]`);
    await deleteButton.click();

    // Confirm deletion
    await page.waitForSelector('p-confirmdialog');
    await page.click('p-confirmdialog button.p-confirm-dialog-accept');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify department is removed
    await page.waitForTimeout(1000);
    expect(await page.textContent('p-treetable')).not.toContain(TEST_DATA.department.name);
  });
});

test.describe('Position CRUD Operations', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await navigateToPositions(page);
  });

  test('should create a new position', async ({ page }) => {
    // Click "New" button
    await page.click('button:has-text("Thêm vị trí")');

    // Wait for dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Fill position form
    await page.fill('input[formcontrolname="code"]', TEST_DATA.position.code);
    await page.fill('input[formcontrolname="name"]', TEST_DATA.position.name);

    // Select department and role (using actual PrimeNG selectors)
    await page.click('p-treeselect[formcontrolname="departmentId"]');
    await page.click('li:has-text("Test Department")');

    await page.click('p-select[formcontrolname="roleId"]');
    await page.click('li:has-text("Employee")');

    // Save position
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify position appears in table
    await page.waitForSelector(`text=${TEST_DATA.position.name}`);
    expect(await page.textContent('p-table')).toContain(TEST_DATA.position.name);
  });

  test('should edit a position', async ({ page }) => {
    // Find and click edit button
    const editButton = page.locator(`tr:has-text("${TEST_DATA.position.name}") button[icon="pi pi-pencil"]`);
    await editButton.click();

    // Wait for edit dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Update position name
    const updatedName = TEST_DATA.position.name + ' Updated';
    await page.fill('input[formcontrolname="name"]', updatedName);

    // Save changes
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify updated name
    await page.waitForSelector(`text=${updatedName}`);
    expect(await page.textContent('p-table')).toContain(updatedName);
  });

  test('should delete a position', async ({ page }) => {
    // Find and click delete button
    const deleteButton = page.locator(`tr:has-text("${TEST_DATA.position.name}") button[icon="pi pi-trash"]`);
    await deleteButton.click();

    // Confirm deletion
    await page.waitForSelector('p-confirmdialog');
    await page.click('p-confirmdialog button.p-confirm-dialog-accept');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify position is removed
    await page.waitForTimeout(1000);
    expect(await page.textContent('p-table')).not.toContain(TEST_DATA.position.name);
  });
});
