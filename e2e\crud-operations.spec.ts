import { test, expect, Page } from '@playwright/test';

// Test data constants
const TEST_DATA = {
  employee: {
    code: '<PERSON>MP001',
    fullname: '<PERSON>',
    nickname: '<PERSON>',
    email: '<EMAIL>',
    phone: '0764556326',
    identification: '123456789012'
  },
  department: {
    code: 'DEPT001',
    name: 'Test Department'
  },
  position: {
    code: 'POS001',
    name: 'Test Position'
  }
};

// Helper functions
async function login(page: Page) {
  // Navigate to login page
  await page.goto('/auth/login');

  // Wait for login form to be fully loaded
  await page.waitForSelector('#username');
  await page.waitForSelector('#password1 input');
  await page.waitForSelector('p-button[label="Đăng nhập"]');

  // Fill login form using actual selectors
  await page.fill('#username', 'admin');
  await page.fill('#password1 input', '12345678');

  // Click login and wait for navigation
  await page.click('p-button[label="Đăng nhập"]');
  await page.waitForLoadState('networkidle');

  // Wait for the main layout to load
  await page.waitForSelector('.layout-wrapper', { timeout: 10000 });
  await page.waitForSelector('.layout-topbar', { timeout: 5000 });
}

async function navigateToEmployees(page: Page) {
  await page.goto('/hr/employees');
  await page.waitForLoadState('networkidle');
  await page.waitForSelector('generic-p-table', { timeout: 10000 });
  // Wait for table to finish loading
  await page.waitForSelector('generic-p-table p-table', { timeout: 5000 });
}

async function navigateToDepartments(page: Page) {
  await page.goto('/system/settings/departments');
  await page.waitForLoadState('networkidle');
  await page.waitForSelector('p-treetable', { timeout: 10000 });
  // Wait for tree table to finish loading
  await page.waitForFunction(() => !document.querySelector('.p-treetable-loading'), { timeout: 5000 });
}

async function navigateToPositions(page: Page) {
  await page.goto('/system/settings/positions');
  await page.waitForLoadState('networkidle');
  await page.waitForSelector('p-table', { timeout: 10000 });
  // Wait for table to finish loading
  await page.waitForFunction(() => !document.querySelector('.p-datatable-loading'), { timeout: 5000 });
}

test.describe('Employee CRUD Operations', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await navigateToEmployees(page);
  });

  test('should create a new employee', async ({ page }) => {
    // Click "New" button to open creation stepper dialog
    await page.click('button:has(span.pi-plus)');

    // Wait for stepper dialog to appear and be fully loaded
    await page.waitForSelector('p-dialog .p-dialog-header:has-text("Thêm nhân viên mới")', { timeout: 10000 });
    await page.waitForSelector('p-stepper', { timeout: 5000 });
    await page.waitForSelector('#code', { timeout: 5000 });

    // Fill employee form using actual field IDs from the stepper
    await page.fill('#code', TEST_DATA.employee.code);
    await page.fill('#fullname', TEST_DATA.employee.fullname);
    await page.fill('#nickname', TEST_DATA.employee.nickname);
    await page.fill('#email', TEST_DATA.employee.email);
    await page.fill('#phone', TEST_DATA.employee.phone);
    await page.fill('#identification', TEST_DATA.employee.identification);

    // Wait for "Tiếp theo" button to be enabled and click it
    await page.waitForSelector('button:has-text("Tiếp theo"):not([disabled])', { timeout: 5000 });
    await page.click('button:has-text("Tiếp theo")');

    // Wait for step 2 (Position step) to be active
    await page.waitForSelector('.p-step-active:has-text("Vị trí")', { timeout: 5000 });
    await page.waitForSelector('#rootDepartmentId', { timeout: 5000 });

    // Fill position information
    // 1. Select root department first
    await page.click('#rootDepartmentId');
    await page.waitForSelector('p-overlay .p-select-list', { timeout: 5000 });
    // Select the first available department option
    await page.click('p-overlay .p-select-list li:first-child');

    // 2. Wait for position dropdown to be enabled and select position
    await page.waitForSelector('#positionId:not(.p-disabled)', { timeout: 5000 });
    await page.click('#positionId');
    await page.waitForSelector('p-overlay .p-select-list', { timeout: 5000 });
    // Select the first available position option
    await page.click('p-overlay .p-select-list li:first-child');

    // 3. Fill start date
    await page.waitForSelector('#fromDate input:not([disabled])', { timeout: 5000 });
    await page.fill('#fromDate input', '01/01/2024');

    // 4. Select team/subdept
    await page.waitForSelector('#departmentId:not(.p-disabled)', { timeout: 5000 });
    await page.click('#departmentId .p-treeselect-dropdown');
    await page.waitForSelector('p-overlay .p-tree', { timeout: 5000 });
    // Select the first available department in tree
    await page.click('p-overlay .p-tree .p-tree-node:first-child .p-tree-node-content');

    // Wait for next button to be enabled and click it
    await page.waitForSelector('button:has-text("Tiếp theo"):not([disabled])', { timeout: 5000 });
    await page.click('button:has-text("Tiếp theo")');


    // Wait for step 3 (Probation step) if it exists
    const probationStep = page.locator('.p-step-active:has-text("Thử việc")');
    if (await probationStep.count() > 0) {
      // Handle probation step if needed
      const finishButton = page.locator('button:has-text("Hoàn thành"), button:has-text("Lưu")');
      if (await finishButton.count() > 0) {
        await finishButton.click();
      }
    }

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success', { timeout: 10000 });

    // Wait for dialog to close
    await page.waitForSelector('p-dialog', { state: 'hidden', timeout: 5000 });

    // Wait for table to refresh and verify employee appears
    await page.waitForTimeout(2000);
    await page.waitForSelector(`text=${TEST_DATA.employee.fullname}`, { timeout: 10000 });
    expect(await page.textContent('generic-p-table')).toContain(TEST_DATA.employee.fullname);
  });

  test('should edit an existing employee', async ({ page }) => {
    // Wait for table to be loaded first
    await page.waitForSelector('generic-p-table tbody tr', { timeout: 10000 });

    // Find and click edit button for the test employee
    const editButton = page.locator(`tr:has-text("${TEST_DATA.employee.fullname}") button[icon="pi pi-pencil"]`);
    await editButton.waitFor({ timeout: 5000 });
    await editButton.click();

    // Wait for edit dialog (simple dialog, not stepper) and form to load
    await page.waitForSelector('edit-create-dialog p-dialog', { timeout: 10000 });
    await page.waitForSelector('input[formcontrolname="fullname"]', { timeout: 5000 });

    // Clear and modify employee data
    const updatedName = TEST_DATA.employee.fullname + ' Updated';
    await page.fill('input[formcontrolname="fullname"]', '');
    await page.fill('input[formcontrolname="fullname"]', updatedName);

    // Wait for form to be valid and save button to be enabled
    await page.waitForSelector('button:has-text("Xác nhận"):not([disabled])', { timeout: 5000 });

    // Save changes
    await page.click('button:has-text("Xác nhận")');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success', { timeout: 10000 });

    // Wait for dialog to close and table to refresh
    await page.waitForSelector('edit-create-dialog p-dialog', { state: 'hidden', timeout: 5000 });
    await page.waitForTimeout(2000);

    // Verify updated name appears in table
    await page.waitForSelector(`text=${updatedName}`, { timeout: 10000 });
    expect(await page.textContent('generic-p-table')).toContain(updatedName);
  });

  test('should delete an employee', async ({ page }) => {
    // Wait for table to be loaded first
    await page.waitForSelector('generic-p-table tbody tr', { timeout: 10000 });

    // Find and click delete button for the test employee
    const deleteButton = page.locator(`tr:has-text("${TEST_DATA.employee.fullname} Updated") button[icon="pi pi-trash"]`);
    await deleteButton.waitFor({ timeout: 5000 });
    await deleteButton.click();

    // Wait for confirmation dialog
    await page.waitForSelector('p-confirmdialog', { timeout: 10000 });
    await page.waitForSelector('p-confirmdialog button.p-confirm-dialog-accept', { timeout: 5000 });

    // Confirm deletion - using the actual confirm dialog button
    await page.click('p-confirmdialog button.p-confirm-dialog-accept');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success', { timeout: 10000 });

    // Wait for table refresh
    await page.waitForTimeout(3000);

    // Verify employee is removed from table
    const tableContent = await page.textContent('generic-p-table');
    expect(tableContent).not.toContain(TEST_DATA.employee.fullname + ' Updated');
  });

  test('should validate required fields in employee form', async ({ page }) => {
    // Click "New" button
    await page.click('button:has(span.pi-plus)');

    // Wait for stepper dialog
    await page.waitForSelector('p-dialog .p-dialog-header:has-text("Thêm nhân viên mới")', { timeout: 10000 });
    await page.waitForSelector('#code', { timeout: 5000 });

    // Try to proceed without filling required fields
    await page.click('button:has-text("Tiếp theo")');

    // Verify validation errors appear (button should remain disabled)
    await page.waitForTimeout(1000);
    const nextButton = page.locator('button:has-text("Tiếp theo")');
    expect(await nextButton.getAttribute('disabled')).toBe('');

    // Verify required fields show validation state
    const codeField = page.locator('#code');
    const fullnameField = page.locator('#fullname');

    // Check if fields have invalid state (may be shown differently in your app)
    expect(await codeField.evaluate(el => el.classList.contains('ng-invalid'))).toBe(true);
    expect(await fullnameField.evaluate(el => el.classList.contains('ng-invalid'))).toBe(true);

    // Close dialog by clicking X button
    await page.click('.p-dialog-close-button');
  });
});

test.describe('Department CRUD Operations', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await navigateToDepartments(page);
  });

  test('should create a new department', async ({ page }) => {
    // Click "New" button - using the actual toolbar button
    await page.click('button:has-text("Thêm phòng ban")');

    // Wait for dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Fill department form
    await page.fill('input[formcontrolname="code"]', TEST_DATA.department.code);
    await page.fill('input[formcontrolname="name"]', TEST_DATA.department.name);

    // Save department
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify department appears in tree table
    await page.waitForSelector(`text=${TEST_DATA.department.name}`);
    expect(await page.textContent('p-treetable')).toContain(TEST_DATA.department.name);
  });

  test('should edit a department', async ({ page }) => {
    // Find and click edit button
    const editButton = page.locator(`tr:has-text("${TEST_DATA.department.name}") button[icon="pi pi-pencil"]`);
    await editButton.click();

    // Wait for edit dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Update department name
    const updatedName = TEST_DATA.department.name + ' Updated';
    await page.fill('input[formcontrolname="name"]', updatedName);

    // Save changes
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify updated name
    await page.waitForSelector(`text=${updatedName}`);
    expect(await page.textContent('p-treetable')).toContain(updatedName);
  });

  test('should delete a department', async ({ page }) => {
    // Find and click delete button
    const deleteButton = page.locator(`tr:has-text("${TEST_DATA.department.name}") button[icon="pi pi-trash"]`);
    await deleteButton.click();

    // Confirm deletion
    await page.waitForSelector('p-confirmdialog');
    await page.click('p-confirmdialog button.p-confirm-dialog-accept');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify department is removed
    await page.waitForTimeout(1000);
    expect(await page.textContent('p-treetable')).not.toContain(TEST_DATA.department.name);
  });
});

test.describe('Position CRUD Operations', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await navigateToPositions(page);
  });

  test('should create a new position', async ({ page }) => {
    // Click "New" button
    await page.click('button:has-text("Thêm vị trí")');

    // Wait for dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Fill position form
    await page.fill('input[formcontrolname="code"]', TEST_DATA.position.code);
    await page.fill('input[formcontrolname="name"]', TEST_DATA.position.name);

    // Select department and role (using actual PrimeNG selectors)
    await page.click('p-treeselect[formcontrolname="departmentId"]');
    await page.click('li:has-text("Test Department")');

    await page.click('p-select[formcontrolname="roleId"]');
    await page.click('li:has-text("Employee")');

    // Save position
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify position appears in table
    await page.waitForSelector(`text=${TEST_DATA.position.name}`);
    expect(await page.textContent('p-table')).toContain(TEST_DATA.position.name);
  });

  test('should edit a position', async ({ page }) => {
    // Find and click edit button
    const editButton = page.locator(`tr:has-text("${TEST_DATA.position.name}") button[icon="pi pi-pencil"]`);
    await editButton.click();

    // Wait for edit dialog
    await page.waitForSelector('edit-create-dialog p-dialog[visible="true"]');

    // Update position name
    const updatedName = TEST_DATA.position.name + ' Updated';
    await page.fill('input[formcontrolname="name"]', updatedName);

    // Save changes
    await page.click('p-button[label="Xác nhận"]');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify updated name
    await page.waitForSelector(`text=${updatedName}`);
    expect(await page.textContent('p-table')).toContain(updatedName);
  });

  test('should delete a position', async ({ page }) => {
    // Find and click delete button
    const deleteButton = page.locator(`tr:has-text("${TEST_DATA.position.name}") button[icon="pi pi-trash"]`);
    await deleteButton.click();

    // Confirm deletion
    await page.waitForSelector('p-confirmdialog');
    await page.click('p-confirmdialog button.p-confirm-dialog-accept');

    // Wait for success toast
    await page.waitForSelector('.p-toast-message-success');

    // Verify position is removed
    await page.waitForTimeout(1000);
    expect(await page.textContent('p-table')).not.toContain(TEST_DATA.position.name);
  });
});
