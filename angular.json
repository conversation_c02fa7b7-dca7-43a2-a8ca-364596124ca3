{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"weset-fe": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/weset-fe", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/ngx-spinner/animations/ball-fall.css", "./node_modules/leaflet/dist/leaflet.css", "src/styles.scss"], "scripts": ["node_modules/leaflet/dist/leaflet.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "production-mobile": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "none", "baseHref": "./", "deployUrl": "./"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "development-optimized": {"optimization": {"scripts": true, "styles": false, "fonts": false}, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"hmr": false}, "configurations": {"production": {"buildTarget": "weset-fe:build:production"}, "production-mobile": {"buildTarget": "weset-fe:build:production-mobile"}, "development": {"buildTarget": "weset-fe:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "e2e": {"builder": "playwright-ng-schematics:playwright", "options": {"devServerTarget": "weset-fe:serve"}, "configurations": {"production": {"devServerTarget": "weset-fe:serve:production"}}}}}}, "cli": {"analytics": false}}