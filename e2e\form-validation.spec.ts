import { test, expect, Page } from '@playwright/test';

// Helper functions
async function login(page: Page) {
  await page.goto('/auth/login');
  await page.fill('#username', 'admin');
  await page.fill('#password1 input', '12345678');
  await page.click('p-button[label="Đăng nhập"]');
  await page.waitForURL('**/**');
}

async function openEmployeeDialog(page: Page) {
  await page.goto('/admin/hr/employees');
  await page.waitForSelector('generic-p-table');
  await page.click('[data-testid="new-employee-button"]');
  await page.waitForSelector('edit-create-dialog[visible="true"]');
}

async function openDepartmentDialog(page: Page) {
  await page.goto('/admin/system/settings/departments');
  await page.waitForSelector('p-treetable');
  await page.click('[data-testid="new-department-button"]');
  await page.waitForSelector('edit-create-dialog[visible="true"]');
}

test.describe('Form Validation Tests', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
  });

  test.describe('Employee Form Validation', () => {
    test('should validate required employee fields', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Try to save without filling any fields
      await page.click('[data-testid="confirm-button"]');
      
      // Check for validation errors on required fields
      await expect(page.locator('[data-testid="employee-code"]')).toHaveClass(/p-invalid/);
      await expect(page.locator('[data-testid="employee-fullname"]')).toHaveClass(/p-invalid/);
      
      // Verify error messages appear
      await expect(page.locator('.p-error')).toHaveCount(2);
    });

    test('should validate employee code format', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Enter invalid code format
      await page.fill('[data-testid="employee-code"]', 'invalid code with spaces');
      await page.fill('[data-testid="employee-fullname"]', 'Test Name');
      
      // Try to save
      await page.click('[data-testid="confirm-button"]');
      
      // Check for code validation error
      await expect(page.locator('[data-testid="employee-code"]')).toHaveClass(/p-invalid/);
      await expect(page.locator('text=Mã nhân viên không hợp lệ')).toBeVisible();
    });

    test('should validate email format', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Fill required fields
      await page.fill('[data-testid="employee-code"]', 'EMP001');
      await page.fill('[data-testid="employee-fullname"]', 'Test Name');
      
      // Enter invalid email
      await page.fill('[data-testid="employee-email"]', 'invalid-email');
      
      // Try to save
      await page.click('[data-testid="confirm-button"]');
      
      // Check for email validation error
      await expect(page.locator('[data-testid="employee-email"]')).toHaveClass(/p-invalid/);
      await expect(page.locator('text=Email không hợp lệ')).toBeVisible();
    });

    test('should validate phone number format', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Fill required fields
      await page.fill('[data-testid="employee-code"]', 'EMP001');
      await page.fill('[data-testid="employee-fullname"]', 'Test Name');
      
      // Enter invalid phone number
      await page.fill('[data-testid="employee-phone"]', '123');
      
      // Try to save
      await page.click('[data-testid="confirm-button"]');
      
      // Check for phone validation error
      await expect(page.locator('[data-testid="employee-phone"]')).toHaveClass(/p-invalid/);
      await expect(page.locator('text=Số điện thoại không hợp lệ')).toBeVisible();
    });

    test('should validate identification number format', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Fill required fields
      await page.fill('[data-testid="employee-code"]', 'EMP001');
      await page.fill('[data-testid="employee-fullname"]', 'Test Name');
      
      // Enter invalid identification number
      await page.fill('[data-testid="employee-identification"]', '123');
      
      // Try to save
      await page.click('[data-testid="confirm-button"]');
      
      // Check for identification validation error
      await expect(page.locator('[data-testid="employee-identification"]')).toHaveClass(/p-invalid/);
      await expect(page.locator('text=CMND/CCCD không hợp lệ')).toBeVisible();
    });

    test('should clear validation errors when fields are corrected', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Try to save without filling fields to trigger validation
      await page.click('[data-testid="confirm-button"]');
      
      // Verify validation errors exist
      await expect(page.locator('[data-testid="employee-code"]')).toHaveClass(/p-invalid/);
      
      // Fill the field correctly
      await page.fill('[data-testid="employee-code"]', 'EMP001');
      
      // Validation error should be cleared
      await expect(page.locator('[data-testid="employee-code"]')).not.toHaveClass(/p-invalid/);
    });
  });

  test.describe('Department Form Validation', () => {
    test('should validate required department fields', async ({ page }) => {
      await openDepartmentDialog(page);
      
      // Try to save without filling any fields
      await page.click('[data-testid="confirm-button"]');
      
      // Check for validation errors
      await expect(page.locator('[data-testid="department-code"]')).toHaveClass(/p-invalid/);
      await expect(page.locator('[data-testid="department-name"]')).toHaveClass(/p-invalid/);
    });

    test('should validate department code uniqueness', async ({ page }) => {
      await openDepartmentDialog(page);
      
      // Enter duplicate department code (assuming one exists)
      await page.fill('[data-testid="department-code"]', 'EXISTING_CODE');
      await page.fill('[data-testid="department-name"]', 'Test Department');
      
      // Try to save
      await page.click('[data-testid="confirm-button"]');
      
      // Check for uniqueness error
      await expect(page.locator('text=Mã phòng ban đã tồn tại')).toBeVisible();
    });
  });

  test.describe('Form Interaction Tests', () => {
    test('should enable/disable save button based on form validity', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Save button should be disabled initially
      await expect(page.locator('[data-testid="confirm-button"]')).toBeDisabled();
      
      // Fill required fields
      await page.fill('[data-testid="employee-code"]', 'EMP001');
      await page.fill('[data-testid="employee-fullname"]', 'Test Name');
      
      // Save button should be enabled
      await expect(page.locator('[data-testid="confirm-button"]')).toBeEnabled();
    });

    test('should reset form when dialog is cancelled and reopened', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Fill some fields
      await page.fill('[data-testid="employee-code"]', 'EMP001');
      await page.fill('[data-testid="employee-fullname"]', 'Test Name');
      
      // Cancel dialog
      await page.click('[data-testid="cancel-button"]');
      
      // Reopen dialog
      await page.click('[data-testid="new-employee-button"]');
      await page.waitForSelector('edit-create-dialog[visible="true"]');
      
      // Fields should be empty
      await expect(page.locator('[data-testid="employee-code"]')).toHaveValue('');
      await expect(page.locator('[data-testid="employee-fullname"]')).toHaveValue('');
    });

    test('should show loading state during form submission', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Fill valid form data
      await page.fill('[data-testid="employee-code"]', 'EMP001');
      await page.fill('[data-testid="employee-fullname"]', 'Test Name');
      await page.fill('[data-testid="employee-email"]', '<EMAIL>');
      
      // Click save and check for loading state
      await page.click('[data-testid="confirm-button"]');
      
      // Button should show loading state
      await expect(page.locator('[data-testid="confirm-button"]')).toHaveClass(/p-button-loading/);
    });

    test('should handle form submission errors gracefully', async ({ page }) => {
      // Mock network to return error
      await page.route('**/api/employees', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });

      await openEmployeeDialog(page);
      
      // Fill valid form data
      await page.fill('[data-testid="employee-code"]', 'EMP001');
      await page.fill('[data-testid="employee-fullname"]', 'Test Name');
      
      // Submit form
      await page.click('[data-testid="confirm-button"]');
      
      // Should show error toast
      await expect(page.locator('.p-toast-message-error')).toBeVisible();
      
      // Dialog should remain open
      await expect(page.locator('edit-create-dialog[visible="true"]')).toBeVisible();
    });
  });

  test.describe('Accessibility Tests', () => {
    test('should have proper ARIA labels and roles', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Check dialog has proper role
      await expect(page.locator('edit-create-dialog')).toHaveAttribute('role', 'dialog');
      
      // Check form fields have proper labels
      await expect(page.locator('[data-testid="employee-code"]')).toHaveAttribute('aria-label');
      await expect(page.locator('[data-testid="employee-fullname"]')).toHaveAttribute('aria-label');
      
      // Check buttons have proper labels
      await expect(page.locator('[data-testid="confirm-button"]')).toHaveAttribute('aria-label');
      await expect(page.locator('[data-testid="cancel-button"]')).toHaveAttribute('aria-label');
    });

    test('should support keyboard navigation', async ({ page }) => {
      await openEmployeeDialog(page);
      
      // Tab through form fields
      await page.keyboard.press('Tab');
      await expect(page.locator('[data-testid="employee-code"]')).toBeFocused();
      
      await page.keyboard.press('Tab');
      await expect(page.locator('[data-testid="employee-fullname"]')).toBeFocused();
      
      // Escape should close dialog
      await page.keyboard.press('Escape');
      await expect(page.locator('edit-create-dialog[visible="true"]')).not.toBeVisible();
    });
  });
});
